#pragma once

//=============================================================================
// ClipSHOOK 地区锁定绕过配置
// 用于绕过Clip Studio Paint的中国大陆地区锁定
//=============================================================================

// 地区绕过配置
namespace RegionBypassConfig {
    
    // 是否启用地区绕过功能
    constexpr bool ENABLE_REGION_BYPASS = true;
    
    // 伪装的语言ID设置
    // 0x0409 = en-US (美国英语)
    // 0x0411 = ja-JP (日本语)  
    // 0x0804 = zh-CN (中文简体) - 不推荐，会触发地区锁定
    // 0x0404 = zh-TW (中文繁体)
    // 0x0c04 = zh-HK (中文香港)
    constexpr LANGID FAKE_LANGUAGE_ID = 0x0409; // 默认使用美国英语
    
    // 伪装的国家/地区代码
    constexpr const wchar_t* FAKE_COUNTRY_CODE = L"1";    // 美国国家代码
    constexpr const wchar_t* FAKE_ISO_CODE = L"US";       // 美国ISO代码
    
    // 调试选项
    constexpr bool ENABLE_DEBUG_OUTPUT = true;  // 是否输出调试信息
    
    // Hook的API列表（可以选择性启用/禁用）
    constexpr bool HOOK_GetSystemDefaultUILanguage = true;
    constexpr bool HOOK_GetUserDefaultUILanguage = true;
    constexpr bool HOOK_GetSystemDefaultLangID = true;
    constexpr bool HOOK_GetUserDefaultLangID = true;
    constexpr bool HOOK_GetLocaleInfoW = true;
    
    // 高级选项：是否Hook更多地区相关API
    constexpr bool HOOK_EXTENDED_LOCALE_APIS = false;  // 暂时禁用，避免过度Hook
}

//=============================================================================
// 支持的语言ID参考
//=============================================================================
/*
常用语言ID：
0x0409 - en-US (美国英语) - 推荐，最不容易触发地区检测
0x0411 - ja-JP (日本语) - 推荐，CSP原产国
0x0c09 - en-AU (澳大利亚英语)
0x1009 - en-CA (加拿大英语)
0x0809 - en-GB (英国英语)
0x040c - fr-FR (法国语)
0x0407 - de-DE (德语)
0x0410 - it-IT (意大利语)
0x0413 - nl-NL (荷兰语)
0x0416 - pt-BR (葡萄牙语-巴西)
0x040a - es-ES (西班牙语)
0x041d - sv-SE (瑞典语)
0x0414 - nb-NO (挪威语)
0x0406 - da-DK (丹麦语)
0x040b - fi-FI (芬兰语)

不推荐的语言ID（可能触发地区检测）：
0x0804 - zh-CN (中文简体) - 中国大陆
0x0404 - zh-TW (中文繁体) - 可能安全
0x0c04 - zh-HK (中文香港) - 可能安全
0x1404 - zh-MO (中文澳门) - 可能安全
0x1004 - zh-SG (中文新加坡) - 可能安全
*/

//=============================================================================
// 使用说明
//=============================================================================
/*
1. 修改 FAKE_LANGUAGE_ID 来改变伪装的语言
2. 如果遇到问题，可以尝试不同的语言ID
3. 设置 ENABLE_DEBUG_OUTPUT = true 来查看Hook状态
4. 如果某个Hook导致问题，可以单独禁用对应的API Hook

注意事项：
- 修改配置后需要重新编译DLL
- 建议使用美国英语(0x0409)或日语(0x0411)
- 避免使用中文简体(0x0804)，会直接触发地区锁定
- 如果CSP界面语言异常，可以在CSP设置中手动调整
*/
