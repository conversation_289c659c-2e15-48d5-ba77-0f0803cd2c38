# ClipSHOOK version.dll 劫持部署指南

## 问题说明
当将ClipSHOOK.dll重命名为version.dll进行劫持时，出现"无法定位程序输入点VerQueryValueW"错误。这是因为CSP需要调用真正的version.dll函数，但我们的DLL没有正确转发这些调用。

## 解决方案

### 方案1：备份原始DLL + 转发（推荐）

#### 步骤1：备份原始version.dll
1. 找到CSP安装目录中的原始`version.dll`
2. 将其重命名为`version_orig.dll`
3. 放在CSP.exe同目录下

#### 步骤2：部署ClipSHOOK
1. 编译ClipSHOOK项目（已启用导出转发）
2. 将生成的`ClipSHOOK.dll`重命名为`version.dll`
3. 放在CSP.exe同目录下

#### 步骤3：验证部署
```
CSP安装目录/
├── CLIPStudioPaint.exe
├── version.dll          <- ClipSHOOK重命名
├── version_orig.dll     <- 原始version.dll备份
└── ... 其他文件
```

### 方案2：系统目录转发

#### 步骤1：使用系统version.dll
修改dllmain.cpp中的转发路径：
```cpp
#pragma comment(linker, "/EXPORT:VerQueryValueW=C:\\Windows\\System32\\version.VerQueryValueW")
// ... 其他导出
```

#### 步骤2：重新编译和部署
1. 修改转发路径后重新编译
2. 将ClipSHOOK.dll重命名为version.dll
3. 放在CSP.exe同目录下

### 方案3：动态加载转发（最灵活）

#### 实现动态转发
在dllmain.cpp中添加动态加载代码：

```cpp
// 全局变量
static HMODULE hOriginalDll = NULL;

// DLL_PROCESS_ATTACH时加载原始DLL
case DLL_PROCESS_ATTACH:
    // 先尝试从系统目录加载
    hOriginalDll = LoadLibraryW(L"C:\\Windows\\System32\\version.dll");
    if (!hOriginalDll) {
        // 备选：从当前目录加载备份
        hOriginalDll = LoadLibraryW(L"version_orig.dll");
    }
    
    // 初始化Hook功能
    CSPHook::Init((uintptr_t)hModule);
    CSPHook::SetUpHook();
    break;

// 导出函数实现
extern "C" __declspec(dllexport) BOOL WINAPI VerQueryValueW(
    LPCVOID pBlock, LPCWSTR lpSubBlock, LPVOID* lplpBuffer, PUINT puLen)
{
    if (hOriginalDll) {
        typedef BOOL(WINAPI* VerQueryValueW_t)(LPCVOID, LPCWSTR, LPVOID*, PUINT);
        VerQueryValueW_t originalFunc = (VerQueryValueW_t)GetProcAddress(hOriginalDll, "VerQueryValueW");
        if (originalFunc) {
            return originalFunc(pBlock, lpSubBlock, lplpBuffer, puLen);
        }
    }
    return FALSE;
}
```

## 当前配置状态

ClipSHOOK已配置为使用方案1（备份原始DLL + 转发）：
- 导出转发指向`version_orig.dll`
- 需要将原始version.dll重命名为version_orig.dll

## 部署检查清单

### 编译前检查
- [ ] 确认dllmain.cpp中的导出转发已启用
- [ ] 确认转发路径正确（version_orig）
- [ ] 确认地区绕过功能已配置

### 部署前检查
- [ ] 备份原始version.dll为version_orig.dll
- [ ] 编译生成ClipSHOOK.dll
- [ ] 将ClipSHOOK.dll重命名为version.dll

### 部署后检查
- [ ] CSP能正常启动，无错误提示
- [ ] 使用DebugView查看Hook日志
- [ ] 确认地区绕过功能生效
- [ ] 测试CSP核心功能正常

## 故障排除

### 错误：无法定位程序输入点
**原因**：缺少函数导出或转发路径错误
**解决**：
1. 检查version_orig.dll是否存在
2. 确认导出转发语法正确
3. 使用Dependency Walker检查导出函数

### 错误：CSP启动失败
**原因**：DLL加载失败或Hook冲突
**解决**：
1. 恢复原始version.dll测试
2. 检查DLL依赖项
3. 禁用部分Hook功能测试

### 错误：地区绕过无效
**原因**：Hook未生效或配置错误
**解决**：
1. 检查DebugView输出
2. 确认RegionBypass配置
3. 测试不同语言ID设置

## 推荐部署流程

1. **备份环境**
   ```cmd
   copy "CSP安装目录\version.dll" "CSP安装目录\version_orig.dll"
   ```

2. **编译ClipSHOOK**
   - 使用Release x64配置
   - 确认无编译错误

3. **部署DLL**
   ```cmd
   copy "ClipSHOOK.dll" "CSP安装目录\version.dll"
   ```

4. **测试功能**
   - 启动CSP验证无错误
   - 使用DebugView查看日志
   - 测试地区绕过效果

5. **回滚方案**（如有问题）
   ```cmd
   del "CSP安装目录\version.dll"
   copy "CSP安装目录\version_orig.dll" "CSP安装目录\version.dll"
   ```

## 注意事项

1. **权限要求**：可能需要管理员权限操作CSP目录
2. **杀毒软件**：可能被误报，需要添加白名单
3. **CSP更新**：CSP更新可能覆盖version.dll，需要重新部署
4. **备份重要**：始终保留原始version.dll的备份
