# ClipSHOOK 动态转发部署指南

## 概述
ClipSHOOK现在使用动态转发技术，可以自动找到并加载原始version.dll，无需手动备份和重命名。

## 动态转发优势

### ✅ 优点
- **自动查找**：自动从系统目录加载原始version.dll
- **无需备份**：不需要手动重命名原始DLL
- **更安全**：避免文件操作错误
- **更灵活**：支持多种加载路径
- **易部署**：只需替换一个文件

### 🔧 工作原理
1. ClipSHOOK启动时自动加载原始version.dll
2. 所有version.dll函数调用动态转发到原始DLL
3. 同时执行ClipSHOOK的Hook功能

## 部署步骤

### 方法1：直接替换（推荐）
```cmd
# 1. 编译ClipSHOOK.dll
# 2. 备份原始version.dll（可选，用于恢复）
copy "CSP目录\version.dll" "CSP目录\version.dll.bak"

# 3. 直接替换
copy "ClipSHOOK.dll" "CSP目录\version.dll"
```

### 方法2：使用备份文件
```cmd
# 1. 重命名原始version.dll
ren "CSP目录\version.dll" "version_orig.dll"

# 2. 部署ClipSHOOK
copy "ClipSHOOK.dll" "CSP目录\version.dll"
```

## 加载优先级

ClipSHOOK会按以下顺序查找原始version.dll：

1. **系统目录**：`C:\Windows\System32\version.dll`
2. **备份文件**：`version_orig.dll`（当前目录）

## 验证部署

### 1. 启动测试
- 启动CSP，应该无错误提示
- 功能正常，无崩溃

### 2. 调试验证
使用DebugView查看输出：
```
[ClipSHOOK] DLL injected successfully!
[ClipSHOOK] Loaded original version.dll from system directory
[ClipSHOOK] Region bypass hooks installed! Fake Language ID: 0x0409
```

### 3. 功能验证
- CSP正常启动，无地区锁定提示
- 所有CSP功能正常工作
- Hook功能生效

## 故障排除

### 问题1：CSP启动失败
**可能原因**：
- ClipSHOOK.dll损坏或不兼容
- 原始version.dll加载失败

**解决方案**：
```cmd
# 恢复原始version.dll
copy "version.dll.bak" "version.dll"
# 或者
del "version.dll"
ren "version_orig.dll" "version.dll"
```

### 问题2：函数调用失败
**症状**：出现"无法定位程序输入点"错误

**解决方案**：
1. 确认系统目录有version.dll
2. 检查version_orig.dll是否存在
3. 重新编译ClipSHOOK

### 问题3：Hook功能无效
**症状**：地区绕过不生效

**解决方案**：
1. 检查DebugView输出
2. 确认RegionBypass配置
3. 验证DLL是否正确加载

## 配置选项

### 地区绕过设置
编辑`RegionBypass.h`：
```cpp
// 启用地区绕过
constexpr bool ENABLE_REGION_BYPASS = true;

// 伪装语言（推荐美国英语）
constexpr LANGID FAKE_LANGUAGE_ID = 0x0409;

// 调试输出
constexpr bool ENABLE_DEBUG_OUTPUT = true;
```

### 高级选项
```cpp
// 选择性启用Hook
constexpr bool HOOK_GetSystemDefaultUILanguage = true;
constexpr bool HOOK_GetUserDefaultUILanguage = true;
constexpr bool HOOK_GetSystemDefaultLangID = true;
constexpr bool HOOK_GetUserDefaultLangID = true;
constexpr bool HOOK_GetLocaleInfoW = true;
```

## 回滚方案

### 完全回滚
```cmd
# 如果有备份
copy "version.dll.bak" "version.dll"

# 如果使用重命名方式
del "version.dll"
ren "version_orig.dll" "version.dll"
```

### 临时禁用
编辑`RegionBypass.h`：
```cpp
constexpr bool ENABLE_REGION_BYPASS = false;
```
然后重新编译部署。

## 注意事项

1. **权限**：可能需要管理员权限操作CSP目录
2. **杀毒软件**：可能被误报，添加白名单
3. **CSP更新**：更新可能覆盖version.dll，需重新部署
4. **备份重要**：始终保留原始文件备份

## 技术细节

### 动态加载机制
```cpp
// 自动从系统目录加载
GetSystemDirectoryW(systemPath, MAX_PATH);
swprintf_s(versionPath, L"%s\\version.dll", systemPath);
g_hOriginalVersionDll = LoadLibraryW(versionPath);

// 备选：从当前目录加载
g_hOriginalVersionDll = LoadLibraryW(L"version_orig.dll");
```

### 函数转发示例
```cpp
extern "C" __declspec(dllexport) BOOL WINAPI VerQueryValueW(
    LPCVOID pBlock, LPCWSTR lpSubBlock, LPVOID* lplpBuffer, PUINT puLen)
{
    if (LoadOriginalVersionDll()) {
        typedef BOOL(WINAPI* VerQueryValueW_t)(LPCVOID, LPCWSTR, LPVOID*, PUINT);
        VerQueryValueW_t originalFunc = (VerQueryValueW_t)GetProcAddress(g_hOriginalVersionDll, "VerQueryValueW");
        if (originalFunc) {
            return originalFunc(pBlock, lpSubBlock, lplpBuffer, puLen);
        }
    }
    return FALSE;
}
```

## 总结

动态转发方案提供了最佳的兼容性和易用性：
- ✅ 无需复杂的文件操作
- ✅ 自动处理函数转发
- ✅ 支持多种部署方式
- ✅ 易于回滚和故障排除

现在您可以直接编译ClipSHOOK.dll并替换CSP目录中的version.dll即可！
