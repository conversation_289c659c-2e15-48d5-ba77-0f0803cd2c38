# ClipSHOOK 正版验证绕过功能指南

## 功能概述

ClipSHOOK的正版验证绕过功能可以绕过Clip Studio Paint的各种限制，包括：
- 正版验证检查
- 保存文件限制
- 版本选择窗口
- 试用版功能限制

## 主要特性

### 🔓 核心绕过功能
- **checkData函数Hook**：绕过最常见的验证检查
- **正版检测函数Hook**：强制返回正版验证通过
- **valueOf函数Hook**：返回有效的许可证信息
- **版本选择窗口绕过**：跳过初始版本选择对话框

### ⚙️ 双重实现方式
- **Hook方式**：拦截函数调用，返回预期值（推荐）
- **代码补丁方式**：直接修改内存中的机器码

### 📋 基于PE_PatchTool的逻辑
- 整合了PE_PatchTool中的所有补丁逻辑
- 支持动态内存补丁，无需修改文件
- 包含特征码匹配和验证

## 配置说明

### 基本配置（LicenseBypass.h）

```cpp
namespace Config {
    // 主要开关
    constexpr bool ENABLE_LICENSE_BYPASS = true;     // 启用正版验证绕过
    constexpr bool ENABLE_DEBUG_OUTPUT = true;       // 启用调试输出
    
    // 实现方式选择
    constexpr bool USE_HOOK_METHOD = true;           // 使用Hook方式（推荐）
    constexpr bool USE_PATCH_METHOD = false;         // 使用代码补丁方式
    
    // Hook选项
    constexpr bool HOOK_CheckData = true;            // Hook checkData函数
    constexpr bool HOOK_CheckLicense = true;         // Hook 正版检测函数
    constexpr bool HOOK_ValueOf = true;              // Hook valueOf函数
    constexpr bool HOOK_SkipSelectWindow = true;     // Hook 跳过版本选择窗口
}
```

### 地址配置（需要根据CSP版本调整）

```cpp
namespace Addresses {
    // 函数地址
    constexpr uintptr_t CheckData_Func = 0x143567B10;      // checkData函数
    constexpr uintptr_t CheckLicense_Func = 0x1434DDDD0;   // 正版检测函数
    constexpr uintptr_t ValueOf_Func = 0x1434da5B0;        // valueOf函数
    constexpr uintptr_t SkipSelectWindow_Func = 0x1434DA340; // 跳过版本选择
    
    // 代码补丁地址
    constexpr uintptr_t SaveFile_Patch = 0x14049CC80;      // 保存文件限制
    constexpr uintptr_t SelectWindow_Patch = 0x1402B2C18;  // 版本选择窗口
}
```

## 功能详解

### 1. checkData函数绕过
```cpp
bool __fastcall Hook_CheckData(void* p1, void* p2) {
    // 强制返回true，绕过所有checkData验证
    return true;
}
```
- **作用**：这是CSP中最常见的验证函数
- **效果**：绕过保存、导出等功能的限制
- **日志**：`[ClipSHOOK] checkData called - returning TRUE (bypassed)`

### 2. 正版检测函数绕过
```cpp
int __fastcall Hook_CheckLicense() {
    // 强制返回1，表示正版验证通过
    return 1;
}
```
- **作用**：绕过核心的正版验证逻辑
- **效果**：让软件认为是正版授权
- **注意**：避免在特定位置返回1导致启动失败

### 3. valueOf函数绕过
```cpp
uintptr_t __fastcall Hook_ValueOf(uintptr_t param) {
    // 返回一个有效的值，表示许可证有效
    return 0x1450209B8;
}
```
- **作用**：返回有效的许可证信息
- **效果**：配合checkData使用，提供完整的验证绕过

### 4. 版本选择窗口绕过
```cpp
void __fastcall Hook_SkipSelectWindow() {
    // 跳过版本选择窗口的逻辑
    return;
}
```
- **作用**：跳过初始的版本选择对话框
- **效果**：直接进入主程序界面，提升用户体验

## 代码补丁功能

### 保存文件限制绕过
```cpp
// 0x14049CC80处改为 jmp 14049CD08
uint8_t op_JMP_14049CD08[5] = { 0xE9, 0x83, 0x00, 0x00, 0x00 };
```
- 基于特征码：`48 89 9D 38 02 00 00 0F 10 80 50 01 00 00 0F 11 85 40 02 00 00`
- 直接跳过保存限制检查逻辑

### 版本选择窗口补丁
```cpp
// 0x1402B2C18处改为 call 0x1434DA340
uint8_t op_Call_SkipSelectWindow[5] = { 0xE8, 0x23, 0x77, 0x22, 0x03 };
```
- 基于特征码：`4C 8D 9C 24 B0 03 00 00 49 8B 5B 38 49 8B 73 40 49 8B E3 41 5F 41 5E 41 5D 41 5C 5F C3`
- 调用跳过逻辑而不是显示选择窗口

### 正版验证补丁
```cpp
// 让1434dddd0处的函数返回1
uint8_t op_mov_eax_1_ret[10] = { 
    0xb8, 0x01, 0x00, 0x00, 0x00,  // mov eax,1
    0xc3, 0x90, 0x90, 0x90, 0x90   // ret nop nop nop nop
};
```
- 直接修改函数返回值为1
- 包含特殊情况的跳转处理

## 使用方法

### 1. 自动启用
正版验证绕过功能默认已集成到ClipSHOOK中，会在DLL加载时自动启用。

### 2. 查看日志
使用DebugView工具查看绕过状态：

```
[ClipSHOOK] Installing license bypass hooks...
[ClipSHOOK] License Bypass - checkData Hook installed: SUCCESS
[ClipSHOOK] License Bypass - CheckLicense Hook installed: SUCCESS
[ClipSHOOK] License Bypass - valueOf Hook installed: SUCCESS
[ClipSHOOK] License Bypass - SkipSelectWindow Hook installed: SUCCESS
[ClipSHOOK] License bypass setup completed successfully!

// 运行时日志
[ClipSHOOK] checkData called - returning TRUE (bypassed)
[ClipSHOOK] License check called - returning 1 (bypassed)
[ClipSHOOK] valueOf(0x1450209B8) called - returning valid value
```

### 3. 自定义配置
修改`LicenseBypass.h`中的配置选项，然后重新编译ClipSHOOK。

## 版本兼容性

### 地址更新
不同版本的CSP可能有不同的函数地址，需要更新`Addresses`命名空间中的地址：

```cpp
// 使用反汇编工具（如x64dbg）找到新地址
constexpr uintptr_t CheckData_Func = 0x新地址;
```

### 特征码匹配
代码补丁方式使用特征码匹配，相对更稳定：
- 自动搜索匹配的代码模式
- 减少版本依赖性
- 更安全的补丁应用

## 故障排除

### 常见问题

**1. CSP启动失败**
- 检查地址是否正确
- 尝试禁用特定Hook
- 使用Hook方式而不是补丁方式

**2. 验证绕过无效**
- 确认Hook是否成功安装
- 检查DebugView输出
- 验证CSP版本兼容性

**3. 功能异常**
- 检查是否Hook了错误的函数
- 确认返回值是否正确
- 验证特殊情况处理

### 调试方法

**1. 启用详细日志**
```cpp
constexpr bool ENABLE_DEBUG_OUTPUT = true;
```

**2. 检查Hook状态**
查看ClipSHOOK启动日志确认Hook安装状态

**3. 单独测试**
可以单独启用某个Hook进行测试：
```cpp
constexpr bool HOOK_CheckData = true;
constexpr bool HOOK_CheckLicense = false;  // 临时禁用
```

## 安全注意事项

1. **仅用于学习**：此功能仅用于学习和研究目的
2. **版本兼容**：确保与您的CSP版本兼容
3. **备份重要**：使用前备份重要文件
4. **合法使用**：请遵守当地法律法规
5. **风险自负**：使用此功能的风险由用户自行承担

## 扩展开发

### 添加新的Hook
```cpp
// 1. 在Addresses中添加地址
constexpr uintptr_t NewFunc = 0x新地址;

// 2. 实现Hook函数
RetType __fastcall Hook_NewFunc(Params...) {
    // Hook逻辑
    return expectedValue;
}

// 3. 在SetupLicenseBypass中注册
CatHook::AutoHook(newFuncAddr, (void*)Hook_NewFunc, (void**)&orig_NewFunc);
```

### 添加新的代码补丁
```cpp
void ApplyNewPatch() {
    uint8_t patchBytes[] = { /* 补丁字节 */ };
    void* patchAddr = (void*)(baseAddr + 偏移地址);
    CatHook::CodePatch(patchAddr, patchBytes, sizeof(patchBytes));
}
```
