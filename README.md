

# CSP流畅调色 ClipStudioPaint-Smooth-Color-Adjustment

使用前请务必详细阅读此Readme文件！

Please read this Readme file carefully before use!

我通过对CSP程序的直接修改，以及CSP插件的开发，解决了当前CSP在调色、模糊功能上的预览不实时的问题。同时也让缩时摄影的导出能自定义视频大小与时长。


Windows版本是在我6年高龄的笔记本上开发的，代码逻辑上与Mac版基本一致，但性能表现上非常不及预期（i7-8750H）。CPU较弱的Windows用户抱歉啦。主频3.5GHZ以上的多核CPU应该能在正常作画的画布大小上有流畅体验。

我不会进行**优动漫**相关的任何开发。
我会提供官方的简中语言包以解决CSP的锁区问题和繁体难以习惯的问题。

为防止出现意料之外的情况影响自己的工作进度，请**在任何替换操作前备份原文件**！

## 正版支持声明

本库不提供除“插件”之外的功能破解，请支持正版。

本库提供的可执行文件（.exe文件）仅修改了动态库引用以将本库的动态库（CSPMOD403.dll/libCSPhook.dylib文件）在CSP启动时静态注入到CSP进程中。

如果因为各种原因需要使用其他人修改的可执行文件，可以自行在其中注入本动态库（推荐静态注入），本动态库会在附加至CSP进程的时候自动执行功能Hook。

## 内容与使用限制

**对于MacOS，只支持M芯片（使用arm64指令集的芯片）的3.2.3版本CSP使用。
对于Windows，只支持x64芯片的4.0.3版本的CSP使用。**
（Intel芯片的Mac用户和Arm芯片的Windows用户果咩那赛）

《MacOS/Windows》文件夹中的内容是对ClipStudioPaint程序进行的修改（Mac：3.2.3，Windows：4.0.3），修改的内容有：插件功能解锁，对常用色调调整及其调整图层的实时预览（色相饱和度明度、色彩平衡、曲线），缩时摄影自定义视频大小与时长，应用后期调色而无需合并图层。Windows目前没有多层调色功能。

《Plugin_***》文件夹的内容是使用ClipStudioPaint的官方插件SDK所开发的两个模糊插件（高斯模糊、动态模糊），可以在支持插件功能的CSP EX中使用（没有特定版本限制）。

## 安装方法

### Windows

将《Plugin_Windows》文件夹中的.cpm文件放入

> C:\Users\\<USER>\AppData\Roaming\CELSYSUserData\CELSYS\CLIPStudioModule\PlugIn\PAINT\

想使用插件请保证CSP为EX版本或者使用我提供的CSP文件.

将《Windows》文件夹中的内容放入4.0.3版本的CSP文件夹内（注意不是那个素材管理软件的文件夹），比如：

> C:\Program Files\CELSYS\CLIP STUDIO 1.5\CLIP STUDIO PAINT\

### MacOS

对于《Plugin_MacOS》文件夹中的.cpm文件，请保证你的CSP可以使用插件功能。打开访达，进入“文稿”文件夹，里面有个名为CELSYS的快捷方式，进入上述CELSYS文件夹后再进入其中的CLIPStudioModule/Plugin/PAINT文件夹，将.cpm文件放在这里面。

  查看3.2.3版本ClipStudioPaint的程序包内容（注意不是ClipStudio），找到其中的MacOS文件夹，将我提供的MacOS文件夹里的所有内容放入其中。

## 使用方法


调色功能按以往的方式直接使用即可。


缩时摄影在导出时会再弹出一个对话框让你填写视频大小和时长，这里你可以不再受选项的限制，自己按需导出，导出大于1280像素大小的视频也是可以的，因为本来就录制得很大，只是CSP不让你导出大的。

模糊功能在滤镜菜单里，进入滤镜菜单下的“流畅调色”子菜单可以找到。

批量应用后期功能（目前仅MacOS版）是一个多图层调色功能，入口是“编辑”菜单中的“应用调整”。比如当画到一半的时候在顶层进行调色或者制作后期效果，但自己有分图层的习惯的话，这个功能能让你的中期调色和画面处理应用到其下的每个图层中，这样你能在保留分层的情况下继续作画。

如果要使用这个功能，自己的基础图层不能有如“正片叠底”这样的特殊模式的图层，必须都是普通的图层。需要创建一个名为“EFFECTS”的图层组，在这个组里创建图层进行画面处理（不要再建子图层组了）。在这个组里进行调色或者打光等操作，然后点击“应用调整”后，软件会将这个EFFECTS图层组复制到其下的每个 **未锁定图层** 的上方并执行“创建剪辑蒙版”“向下合并”的操作，执行完后，你所做的调整就直接应用在了所有图层中并且依然保留以前的分层状态。但其本质是一个带有一些条件判断的“自动动作”功能，所以是没办法一键撤销的，CSP没有PS那样的快照功能，所以使用前一定要保存文件！（这个功能没法保证图层组应用前后的画面完全一致，差异主要是半透明像素的叠加导致的）

  

## 注意事项


*色相饱和度明度的色调调整和新添加的滤镜不要在宽高超过16384像素的画布中使用。（由图形接口的纹理大小限制，纹理边长最大16384像素）

*由于CSP的狗屎底层，就算用GPU加速瞬间把画面渲染出来，也要花时间进拷贝内存才能显示，所以在画布较大的时候也并非流畅。

*如果因为各种原因需要使用其他人修改的可执行文件，可以自行在其中注入本动态库（推荐静态注入），本动态库会在附加至CSP进程的时候自动执行功能Hook。

*我不会经常浏览Github，如果有问题需要我尽快答复的话请不要在这里问，我看不到的。。。可以来b站私信问我或者邮件联系我https://space.bilibili.com/5808772

## 偶发问题

*有用户遇到NAS(网络存储设备)中某工程文件打开时软件（其他文件正常），先打开另一个工程再打开目标工程可以临时解决这个问题。（问题文件在正常保存后不再会出现问题，并且无法复现，可能是单纯的偶然事件，不确定和改动后的CSP是否存在关系。）

## 吐槽


逆向CSP的时候发现这个软件的开发人员几乎不知道多线程和GPU为何物，而且对图层直接调色时（直接编辑图层调色，不是新建调整图层）并不是只修改和预览画布显示的部分，而是在内存中将整个画布修改后再输出画面，这导致了就算我用GPU进行加速，在画布太大时也会因为CSP自身进行超大内存拷贝而限制预览流畅程度。甚至他们还专门写了计时功能，来让你的调色或者滤镜在鼠标停下一会后才开始处理画面，试图这样子来遮自己不能实时渲染的羞。 对于调色的优化，我做的大部分工作其实是找到并跳过CSP的计时逻辑，让CSP能在鼠标运动时就直接开始进行图像处理而不需要等鼠标停下， 就算这样就已经极大改善了使用体验了（当然对于超大画布还是没救的）。

日本程序员刻板印象时刻加深中。这么基础的体验问题能拖到软件出4.0了还不解决是真的👍

想感受CSP狗屎优化可以下载CSP官方的插件SDK看看官方给的示例，示例的HSV（色相饱和度明度）滤镜插件的逻辑和CSP内部自带的HSV调色代码逻辑是几乎相同的。

如果CSP官方哪天突然一激灵改善了基础体验问题，请不要忘记我来过。


Mac到Windows一路逆向和修改已经花了我四五个月的时间，也就是说这期间我没有画任何东西，也没有进行练习，我的意思是说，有人能教我画画吗。
