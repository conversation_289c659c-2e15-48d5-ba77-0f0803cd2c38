/*
 * ClipSHOOK DLL注入器
 * 用于将ClipSHOOK.dll注入到Clip Studio Paint进程中
 */

#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <string>

class DLLInjector {
private:
    DWORD FindProcessId(const std::wstring& processName) {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            return 0;
        }

        PROCESSENTRY32W pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32W);

        if (Process32FirstW(hSnapshot, &pe32)) {
            do {
                if (processName == pe32.szExeFile) {
                    CloseHandle(hSnapshot);
                    return pe32.th32ProcessID;
                }
            } while (Process32NextW(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        return 0;
    }

public:
    bool InjectDLL(DWORD processId, const std::wstring& dllPath) {
        // 打开目标进程
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) {
            std::wcout << L"Failed to open process. Error: " << GetLastError() << std::endl;
            return false;
        }

        // 在目标进程中分配内存
        SIZE_T pathSize = (dllPath.length() + 1) * sizeof(wchar_t);
        LPVOID pRemotePath = VirtualAllocEx(hProcess, NULL, pathSize, MEM_COMMIT, PAGE_READWRITE);
        if (!pRemotePath) {
            std::wcout << L"Failed to allocate memory in target process. Error: " << GetLastError() << std::endl;
            CloseHandle(hProcess);
            return false;
        }

        // 写入DLL路径
        if (!WriteProcessMemory(hProcess, pRemotePath, dllPath.c_str(), pathSize, NULL)) {
            std::wcout << L"Failed to write DLL path to target process. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // 获取LoadLibraryW地址
        HMODULE hKernel32 = GetModuleHandleW(L"kernel32.dll");
        LPTHREAD_START_ROUTINE pLoadLibrary = (LPTHREAD_START_ROUTINE)GetProcAddress(hKernel32, "LoadLibraryW");
        if (!pLoadLibrary) {
            std::wcout << L"Failed to get LoadLibraryW address. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // 创建远程线程
        HANDLE hRemoteThread = CreateRemoteThread(hProcess, NULL, 0, pLoadLibrary, pRemotePath, 0, NULL);
        if (!hRemoteThread) {
            std::wcout << L"Failed to create remote thread. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // 等待线程完成
        WaitForSingleObject(hRemoteThread, INFINITE);

        // 获取线程退出代码（LoadLibrary的返回值）
        DWORD exitCode;
        GetExitCodeThread(hRemoteThread, &exitCode);

        // 清理资源
        CloseHandle(hRemoteThread);
        VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);

        if (exitCode == 0) {
            std::wcout << L"DLL injection failed. LoadLibrary returned NULL." << std::endl;
            return false;
        }

        std::wcout << L"DLL injected successfully! Module handle: 0x" << std::hex << exitCode << std::endl;
        return true;
    }

    bool InjectByProcessName(const std::wstring& processName, const std::wstring& dllPath) {
        DWORD processId = FindProcessId(processName);
        if (processId == 0) {
            std::wcout << L"Process not found: " << processName << std::endl;
            return false;
        }

        std::wcout << L"Found process: " << processName << L" (PID: " << processId << L")" << std::endl;
        return InjectDLL(processId, dllPath);
    }
};

int wmain(int argc, wchar_t* argv[]) {
    std::wcout << L"ClipSHOOK DLL Injector v1.0" << std::endl;
    std::wcout << L"==============================" << std::endl;

    // 检查命令行参数
    if (argc < 2) {
        std::wcout << L"Usage: " << argv[0] << L" <DLL_Path> [Process_Name]" << std::endl;
        std::wcout << L"Example: " << argv[0] << L" ClipSHOOK.dll CLIPStudioPaint.exe" << std::endl;
        return 1;
    }

    std::wstring dllPath = argv[1];
    std::wstring processName = (argc >= 3) ? argv[2] : L"CLIPStudioPaint.exe";

    // 检查DLL文件是否存在
    if (GetFileAttributesW(dllPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
        std::wcout << L"DLL file not found: " << dllPath << std::endl;
        return 1;
    }

    // 转换为绝对路径
    wchar_t fullPath[MAX_PATH];
    if (GetFullPathNameW(dllPath.c_str(), MAX_PATH, fullPath, NULL) == 0) {
        std::wcout << L"Failed to get full path for DLL." << std::endl;
        return 1;
    }

    std::wcout << L"DLL Path: " << fullPath << std::endl;
    std::wcout << L"Target Process: " << processName << std::endl;
    std::wcout << L"==============================" << std::endl;

    DLLInjector injector;
    
    // 尝试注入
    if (injector.InjectByProcessName(processName, fullPath)) {
        std::wcout << L"Injection completed successfully!" << std::endl;
        return 0;
    } else {
        std::wcout << L"Injection failed!" << std::endl;
        return 1;
    }
}

/*
编译命令：
cl /EHsc ClipSHOOK_Injector.cpp /Fe:ClipSHOOK_Injector.exe

使用方法：
1. 编译此文件生成注入器
2. 启动Clip Studio Paint
3. 运行: ClipSHOOK_Injector.exe ClipSHOOK.dll
4. 检查注入是否成功

注意事项：
- 需要管理员权限运行
- 确保DLL和目标进程架构匹配（x86/x64）
- 可能被反病毒软件拦截
*/
