#pragma once






class CSPHook
{
public:
	static void Init(uintptr_t _thisModule);
	static void SetUpHook();

	static uintptr_t GetBaseAddr() { return baseAddr; };

private:
	static bool __fastcall Hook_CheckData(void* p1, void* p2);


	//hook一个在软件已经完全打开后才执行的函数
	//保证一些逻辑在软件打开后才开始执行
	//这里选择的函数是一个 画布窗口数量改变时会经过的函数
	static uintptr_t __fastcall Hook_LateHook(void* p1, void* p2);

	// 地区锁定绕过功能
	static void SetupRegionBypassHook();
	static LANGID WINAPI Hook_GetSystemDefaultUILanguage();
	static LANGID WINAPI Hook_GetUserDefaultUILanguage();
	static LANGID WINAPI Hook_GetSystemDefaultLangID();
	static LANGID WINAPI Hook_GetUserDefaultLangID();
	static int WINAPI Hook_GetLocaleInfoW(LCID Locale, LCTYPE LCType, LPWSTR lpLCData, int cchData);

	static uintptr_t baseAddr;
};

