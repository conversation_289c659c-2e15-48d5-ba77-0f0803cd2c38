// dllmain.cpp : 定义 DLL 应用程序的入口点。
#include "pch.h"
#include"ProjConfig.h"
#include"CSPHook.h"

extern "C" __declspec(dllexport) int FakeFuction(void)
{
    ////do anything here////
    return 0;
}

//=============================================================================
// DLL劫持功能 - version.dll动态转发
// 使用动态加载方式转发到真正的version.dll
//=============================================================================

// 全局变量：原始version.dll句柄
static HMODULE g_hOriginalVersionDll = NULL;

// 动态加载原始version.dll
static BOOL LoadOriginalVersionDll() {
    if (g_hOriginalVersionDll) {
        return TRUE; // 已经加载
    }

    // 方法1：从系统目录加载
    wchar_t systemPath[MAX_PATH];
    if (GetSystemDirectoryW(systemPath, MAX_PATH)) {
        wchar_t versionPath[MAX_PATH];
        wsprintfW(versionPath, L"%s\\version.dll", systemPath);
        g_hOriginalVersionDll = LoadLibraryW(versionPath);
        if (g_hOriginalVersionDll) {
            OutputDebugStringA("[ClipSHOOK] Loaded original version.dll from system directory");
            return TRUE;
        }
    }

    // 方法2：从当前目录加载备份
    g_hOriginalVersionDll = LoadLibraryW(L"version_orig.dll");
    if (g_hOriginalVersionDll) {
        OutputDebugStringA("[ClipSHOOK] Loaded original version.dll from version_orig.dll");
        return TRUE;
    }

    OutputDebugStringA("[ClipSHOOK] Failed to load original version.dll");
    return FALSE;
}

//=============================================================================
// version.dll 函数动态转发实现
//=============================================================================

// 使用.def文件导出避免函数重定义问题
extern "C" BOOL WINAPI _GetFileVersionInfoA_Hook(
    LPCSTR lptstrFilename, DWORD dwHandle, DWORD dwLen, LPVOID lpData)
{
    if (LoadOriginalVersionDll()) {
        typedef BOOL(WINAPI* GetFileVersionInfoA_t)(LPCSTR, DWORD, DWORD, LPVOID);
        GetFileVersionInfoA_t originalFunc = (GetFileVersionInfoA_t)GetProcAddress(g_hOriginalVersionDll, "GetFileVersionInfoA");
        if (originalFunc) {
            return originalFunc(lptstrFilename, dwHandle, dwLen, lpData);
        }
    }
    return FALSE;
}

extern "C" BOOL WINAPI _GetFileVersionInfoW_Hook(
    LPCWSTR lptstrFilename, DWORD dwHandle, DWORD dwLen, LPVOID lpData)
{
    if (LoadOriginalVersionDll()) {
        typedef BOOL(WINAPI* GetFileVersionInfoW_t)(LPCWSTR, DWORD, DWORD, LPVOID);
        GetFileVersionInfoW_t originalFunc = (GetFileVersionInfoW_t)GetProcAddress(g_hOriginalVersionDll, "GetFileVersionInfoW");
        if (originalFunc) {
            return originalFunc(lptstrFilename, dwHandle, dwLen, lpData);
        }
    }
    return FALSE;
}

extern "C" DWORD WINAPI _GetFileVersionInfoSizeA_Hook(
    LPCSTR lptstrFilename, LPDWORD lpdwHandle)
{
    if (LoadOriginalVersionDll()) {
        typedef DWORD(WINAPI* GetFileVersionInfoSizeA_t)(LPCSTR, LPDWORD);
        GetFileVersionInfoSizeA_t originalFunc = (GetFileVersionInfoSizeA_t)GetProcAddress(g_hOriginalVersionDll, "GetFileVersionInfoSizeA");
        if (originalFunc) {
            return originalFunc(lptstrFilename, lpdwHandle);
        }
    }
    return 0;
}

extern "C" DWORD WINAPI _GetFileVersionInfoSizeW_Hook(
    LPCWSTR lptstrFilename, LPDWORD lpdwHandle)
{
    if (LoadOriginalVersionDll()) {
        typedef DWORD(WINAPI* GetFileVersionInfoSizeW_t)(LPCWSTR, LPDWORD);
        GetFileVersionInfoSizeW_t originalFunc = (GetFileVersionInfoSizeW_t)GetProcAddress(g_hOriginalVersionDll, "GetFileVersionInfoSizeW");
        if (originalFunc) {
            return originalFunc(lptstrFilename, lpdwHandle);
        }
    }
    return 0;
}

extern "C" BOOL WINAPI _VerQueryValueA_Hook(
    LPCVOID pBlock, LPCSTR lpSubBlock, LPVOID* lplpBuffer, PUINT puLen)
{
    if (LoadOriginalVersionDll()) {
        typedef BOOL(WINAPI* VerQueryValueA_t)(LPCVOID, LPCSTR, LPVOID*, PUINT);
        VerQueryValueA_t originalFunc = (VerQueryValueA_t)GetProcAddress(g_hOriginalVersionDll, "VerQueryValueA");
        if (originalFunc) {
            return originalFunc(pBlock, lpSubBlock, lplpBuffer, puLen);
        }
    }
    return FALSE;
}

extern "C" BOOL WINAPI _VerQueryValueW_Hook(
    LPCVOID pBlock, LPCWSTR lpSubBlock, LPVOID* lplpBuffer, PUINT puLen)
{
    if (LoadOriginalVersionDll()) {
        typedef BOOL(WINAPI* VerQueryValueW_t)(LPCVOID, LPCWSTR, LPVOID*, PUINT);
        VerQueryValueW_t originalFunc = (VerQueryValueW_t)GetProcAddress(g_hOriginalVersionDll, "VerQueryValueW");
        if (originalFunc) {
            return originalFunc(pBlock, lpSubBlock, lplpBuffer, puLen);
        }
    }
    return FALSE;
}

extern "C" DWORD WINAPI _VerLanguageNameA_Hook(
    DWORD wLang, LPSTR szLang, DWORD cchLang)
{
    if (LoadOriginalVersionDll()) {
        typedef DWORD(WINAPI* VerLanguageNameA_t)(DWORD, LPSTR, DWORD);
        VerLanguageNameA_t originalFunc = (VerLanguageNameA_t)GetProcAddress(g_hOriginalVersionDll, "VerLanguageNameA");
        if (originalFunc) {
            return originalFunc(wLang, szLang, cchLang);
        }
    }
    return 0;
}

extern "C" DWORD WINAPI _VerLanguageNameW_Hook(
    DWORD wLang, LPWSTR szLang, DWORD cchLang)
{
    if (LoadOriginalVersionDll()) {
        typedef DWORD(WINAPI* VerLanguageNameW_t)(DWORD, LPWSTR, DWORD);
        VerLanguageNameW_t originalFunc = (VerLanguageNameW_t)GetProcAddress(g_hOriginalVersionDll, "VerLanguageNameW");
        if (originalFunc) {
            return originalFunc(wLang, szLang, cchLang);
        }
    }
    return 0;
}

extern "C" DWORD WINAPI _VerFindFileA_Hook(
    DWORD uFlags, LPSTR szFileName, LPSTR szWinDir, LPSTR szAppDir,
    LPSTR szCurDir, PUINT lpuCurDirLen, LPSTR szDestDir, PUINT lpuDestDirLen)
{
    if (LoadOriginalVersionDll()) {
        typedef DWORD(WINAPI* VerFindFileA_t)(DWORD, LPSTR, LPSTR, LPSTR, LPSTR, PUINT, LPSTR, PUINT);
        VerFindFileA_t originalFunc = (VerFindFileA_t)GetProcAddress(g_hOriginalVersionDll, "VerFindFileA");
        if (originalFunc) {
            return originalFunc(uFlags, szFileName, szWinDir, szAppDir, szCurDir, lpuCurDirLen, szDestDir, lpuDestDirLen);
        }
    }
    return 0x4; // VFF_CURNEDEST
}

extern "C" DWORD WINAPI _VerFindFileW_Hook(
    DWORD uFlags, LPWSTR szFileName, LPWSTR szWinDir, LPWSTR szAppDir,
    LPWSTR szCurDir, PUINT lpuCurDirLen, LPWSTR szDestDir, PUINT lpuDestDirLen)
{
    if (LoadOriginalVersionDll()) {
        typedef DWORD(WINAPI* VerFindFileW_t)(DWORD, LPWSTR, LPWSTR, LPWSTR, LPWSTR, PUINT, LPWSTR, PUINT);
        VerFindFileW_t originalFunc = (VerFindFileW_t)GetProcAddress(g_hOriginalVersionDll, "VerFindFileW");
        if (originalFunc) {
            return originalFunc(uFlags, szFileName, szWinDir, szAppDir, szCurDir, lpuCurDirLen, szDestDir, lpuDestDirLen);
        }
    }
    return 0x4; // VFF_CURNEDEST
}

extern "C" DWORD WINAPI _VerInstallFileA_Hook(
    DWORD uFlags, LPSTR szSrcFileName, LPSTR szDestFileName, LPSTR szSrcDir,
    LPSTR szDestDir, LPSTR szCurDir, LPSTR szTmpFile, PUINT lpuTmpFileLen)
{
    if (LoadOriginalVersionDll()) {
        typedef DWORD(WINAPI* VerInstallFileA_t)(DWORD, LPSTR, LPSTR, LPSTR, LPSTR, LPSTR, LPSTR, PUINT);
        VerInstallFileA_t originalFunc = (VerInstallFileA_t)GetProcAddress(g_hOriginalVersionDll, "VerInstallFileA");
        if (originalFunc) {
            return originalFunc(uFlags, szSrcFileName, szDestFileName, szSrcDir, szDestDir, szCurDir, szTmpFile, lpuTmpFileLen);
        }
    }
    return 0x10; // VIF_CANNOTCREATE
}

extern "C" DWORD WINAPI _VerInstallFileW_Hook(
    DWORD uFlags, LPWSTR szSrcFileName, LPWSTR szDestFileName, LPWSTR szSrcDir,
    LPWSTR szDestDir, LPWSTR szCurDir, LPWSTR szTmpFile, PUINT lpuTmpFileLen)
{
    if (LoadOriginalVersionDll()) {
        typedef DWORD(WINAPI* VerInstallFileW_t)(DWORD, LPWSTR, LPWSTR, LPWSTR, LPWSTR, LPWSTR, LPWSTR, PUINT);
        VerInstallFileW_t originalFunc = (VerInstallFileW_t)GetProcAddress(g_hOriginalVersionDll, "VerInstallFileW");
        if (originalFunc) {
            return originalFunc(uFlags, szSrcFileName, szDestFileName, szSrcDir, szDestDir, szCurDir, szTmpFile, lpuTmpFileLen);
        }
    }
    return 0x10; // VIF_CANNOTCREATE
}


BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // 输出调试信息，确认DLL已成功注入
        OutputDebugStringA("[ClipSHOOK] DLL injected successfully!");

        // 加载原始version.dll用于函数转发
        LoadOriginalVersionDll();

        // 创建文件标记（可选，用于验证注入）
        #ifdef CLIPSHOOK_DEBUG_MODE
        CreateFileA("C:\\temp\\clipshook_loaded.txt", GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, 0, NULL);
        #endif

        // 初始化Hook功能
        CSPHook::Init((uintptr_t)hModule);
        CSPHook::SetUpHook();
        break;
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    case DLL_PROCESS_DETACH:
        OutputDebugStringA("[ClipSHOOK] DLL unloading...");
        // 释放原始version.dll
        if (g_hOriginalVersionDll) {
            FreeLibrary(g_hOriginalVersionDll);
            g_hOriginalVersionDll = NULL;
        }
        break;
    }
    return TRUE;
}



//CSP PE node
//导入表0x218 ：04BC122C  FOA0x04BC022C.  SIZE 0X550    修改后size：0x550+0x14=0X564