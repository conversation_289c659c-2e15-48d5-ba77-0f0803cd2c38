# ClipSHOOK 重新编译指南

## 🚨 问题分析

从您的DebugView日志可以看出：
```
[ClipSHOOK] Setting up save file bypass...
[ClipSHOOK] Save File Bypass - Searching for save file restriction pattern...: SUCCESS
[ClipSHOOK] Save File Bypass - Save file restriction pattern not found: FAILED
```

这说明您使用的仍然是旧版本的DLL，包含了已删除的SaveFileBypass代码。

## 🔧 解决方案

### 步骤1：完全清理编译缓存

**在Visual Studio中**：
1. 打开ClipSHOOK.sln
2. 点击"生成" -> "清理解决方案"
3. 关闭Visual Studio

**手动清理**（如果需要）：
```cmd
# 删除所有编译输出
rmdir /s /q "x64"
rmdir /s /q "ClipSHOOK\x64"
del /q "*.user"
```

### 步骤2：重新打开项目

1. 重新启动Visual Studio
2. 打开ClipSHOOK.sln
3. 确认配置为Release x64

### 步骤3：验证项目文件

确认以下文件存在：
- ✅ ClipSHOOK/SimpleBypass.h
- ✅ ClipSHOOK/SimpleBypass.cpp
- ❌ ClipSHOOK/SaveFileBypass.h (应该不存在)
- ❌ ClipSHOOK/SaveFileBypass.cpp (应该不存在)

### 步骤4：重新生成解决方案

1. 点击"生成" -> "重新生成解决方案"
2. 等待编译完成
3. 确认无错误

### 步骤5：验证编译结果

**检查编译日志**，应该看到：
```
CSPHook.cpp
dllmain.cpp
HookTool.cpp
LicenseBypass.cpp
SimpleBypass.cpp    <- 新文件
decode.c
...
```

**不应该看到**：
```
SaveFileBypass.cpp  <- 旧文件，不应该出现
```

### 步骤6：部署新DLL

```cmd
# 备份当前version.dll
copy "CSP目录\version.dll" "CSP目录\version.dll.old"

# 部署新编译的DLL
copy "x64\Release\ClipSHOOK.dll" "CSP目录\version.dll"
```

### 步骤7：验证新版本

启动CSP并查看DebugView，应该看到：
```
[ClipSHOOK] Late initialization - setting up bypass functions...
[ClipSHOOK] Setting up simple bypass (NEW VERSION)...
[ClipSHOOK] Simple Bypass - Testing all known addresses: SUCCESS
[ClipSHOOK] Simple Bypass - Address test at 0x143567B10: SUCCESS/FAILED
```

## 🔍 预期的新日志输出

**成功的情况**：
```
[ClipSHOOK] DLL injected successfully!
[ClipSHOOK] Loaded original version.dll from system directory
[ClipSHOOK] Region bypass hooks installed! Fake Language ID: 0x0409
[ClipSHOOK] Late initialization - setting up bypass functions...
[ClipSHOOK] Setting up simple bypass (NEW VERSION)...
[ClipSHOOK] Simple Bypass - Testing all known addresses: SUCCESS
[ClipSHOOK] Simple Bypass - Address test at 0x143567B10: SUCCESS
[ClipSHOOK] Simple Bypass - Found valid known address at 0x143567B10: SUCCESS
[ClipSHOOK] Simple Bypass - checkData Hook installed at 0x143567B10: SUCCESS
[ClipSHOOK] Simple bypass setup completed successfully!
[ClipSHOOK] License bypass disabled by configuration
```

**地址搜索失败的情况**：
```
[ClipSHOOK] Setting up simple bypass (NEW VERSION)...
[ClipSHOOK] Simple Bypass - Testing all known addresses: SUCCESS
[ClipSHOOK] Simple Bypass - Address test at 0x143567B10: FAILED
[ClipSHOOK] Simple Bypass - Address test at 0x143567000: FAILED
[ClipSHOOK] Simple Bypass - Address test at 0x143568000: FAILED
[ClipSHOOK] Simple Bypass - Address test at 0x143560000: FAILED
[ClipSHOOK] Simple Bypass - Pattern search failed: FAILED
[ClipSHOOK] Simple Bypass - All search methods failed: FAILED
[ClipSHOOK] Simple bypass setup failed - no valid address found
```

## 🎯 如果地址搜索失败

### 方法1：手动查找地址

使用x64dbg查找checkData函数：
1. 打开CSP进程
2. 搜索字符串"checkData"或相关错误消息
3. 找到函数地址
4. 更新SimpleBypass.h中的地址

### 方法2：使用更广泛的搜索

修改SimpleBypass.h：
```cpp
namespace Config {
    constexpr bool USE_WIDE_SEARCH = true;
    constexpr size_t SEARCH_RANGE = 0x20000000;  // 增加搜索范围
}
```

### 方法3：添加调试Hook

临时添加一个简单的测试Hook：
```cpp
// 在SimpleBypass.cpp中添加
void TestHook() {
    MessageBoxA(NULL, "Hook Test", "Debug", MB_OK);
}

// 尝试Hook一个已知的API
CatHook::HookEx(L"kernel32.dll", "GetCurrentProcessId", 
    (void*)TestHook, nullptr);
```

## 🚀 快速验证方法

### 验证1：检查DLL时间戳
```cmd
dir "CSP目录\version.dll"
```
确认文件时间是最新的编译时间。

### 验证2：检查DLL大小
新的DLL应该与旧的大小不同（因为代码变化）。

### 验证3：字符串搜索
```cmd
strings version.dll | findstr "NEW VERSION"
```
应该能找到"Setting up simple bypass (NEW VERSION)"字符串。

## ⚠️ 注意事项

1. **完全重启**：清理缓存后完全重启Visual Studio
2. **管理员权限**：确保以管理员权限运行Visual Studio
3. **杀毒软件**：临时禁用杀毒软件避免干扰编译
4. **文件锁定**：确保CSP没有在运行，避免文件被锁定

## 🔄 如果仍然有问题

1. **手动删除所有编译输出**
2. **重新创建项目文件**
3. **使用命令行编译**：
   ```cmd
   msbuild ClipSHOOK.sln /p:Configuration=Release /p:Platform=x64 /t:Rebuild
   ```

现在请按照这个指南重新编译，确保使用的是包含SimpleBypass的新版本！
