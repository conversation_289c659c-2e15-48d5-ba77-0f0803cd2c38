# ClipSHOOK DLL注入指南

## 概述
ClipSHOOK.dll需要被注入到Clip Studio Paint进程中才能发挥Hook功能。以下是几种常用的DLL注入方法。

## 方法1：DLL劫持（推荐）

### 原理
利用Windows DLL搜索顺序，将ClipSHOOK.dll伪装成CSP需要的系统DLL。

### 步骤
1. **查找目标DLL**
   - 使用Process Monitor监控CSP启动时加载的DLL
   - 选择一个CSP会加载但不是核心功能的DLL
   - 常见目标：`version.dll`, `winmm.dll`, `dwmapi.dll`

2. **创建代理DLL**
   - 将ClipSHOOK.dll重命名为目标DLL名称
   - 在DLL中添加原始DLL的导出函数转发
   - 放置在CSP.exe同目录下

3. **示例代码**
```cpp
// 在dllmain.cpp中添加
#pragma comment(linker, "/EXPORT:GetFileVersionInfoA=version.GetFileVersionInfoA")
#pragma comment(linker, "/EXPORT:GetFileVersionInfoW=version.GetFileVersionInfoW")
// ... 其他导出函数
```

## 方法2：SetWindowsHookEx注入

### 原理
使用Windows Hook API将DLL注入到目标进程。

### 实现
```cpp
// 注入器代码
HMODULE hMod = LoadLibrary(L"ClipSHOOK.dll");
HOOKPROC addr = (HOOKPROC)GetProcAddress(hMod, "HookProc");
HHOOK hHook = SetWindowsHookEx(WH_GETMESSAGE, addr, hMod, targetThreadId);
```

## 方法3：CreateRemoteThread注入

### 原理
在目标进程中创建远程线程来加载DLL。

### 实现
```cpp
// 注入器代码
HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
LPVOID pDllPath = VirtualAllocEx(hProcess, NULL, MAX_PATH, MEM_COMMIT, PAGE_READWRITE);
WriteProcessMemory(hProcess, pDllPath, dllPath, strlen(dllPath), NULL);
HANDLE hThread = CreateRemoteThread(hProcess, NULL, 0, 
    (LPTHREAD_START_ROUTINE)LoadLibraryA, pDllPath, 0, NULL);
```

## 方法4：手动DLL映射

### 原理
手动将DLL映射到目标进程内存空间。

### 特点
- 绕过大部分反注入检测
- 不会出现在模块列表中
- 实现复杂度较高

## 推荐方案：DLL劫持

### 为什么推荐DLL劫持？
1. **隐蔽性好** - 看起来像正常的系统DLL
2. **稳定性高** - 不容易被反注入机制检测
3. **实现简单** - 只需重命名和添加导出转发
4. **兼容性好** - 适用于大多数应用程序

### 具体实现步骤

#### 1. 监控CSP加载的DLL
使用Process Monitor：
1. 启动Process Monitor
2. 设置过滤器：Process Name is "CLIPStudioPaint.exe"
3. 启动CSP，观察加载的DLL列表
4. 选择一个合适的目标DLL

#### 2. 修改ClipSHOOK项目
在`dllmain.cpp`中添加导出转发：
```cpp
// 假设劫持version.dll
#pragma comment(linker, "/EXPORT:GetFileVersionInfoA=version.GetFileVersionInfoA")
#pragma comment(linker, "/EXPORT:GetFileVersionInfoW=version.GetFileVersionInfoW")
#pragma comment(linker, "/EXPORT:GetFileVersionInfoSizeA=version.GetFileVersionInfoSizeA")
#pragma comment(linker, "/EXPORT:GetFileVersionInfoSizeW=version.GetFileVersionInfoSizeW")
#pragma comment(linker, "/EXPORT:VerQueryValueA=version.VerQueryValueA")
#pragma comment(linker, "/EXPORT:VerQueryValueW=version.VerQueryValueW")
```

#### 3. 部署
1. 编译ClipSHOOK.dll
2. 重命名为目标DLL名称（如version.dll）
3. 放置在CSP.exe同目录下
4. 启动CSP验证注入成功

## 验证注入成功

### 方法1：日志输出
在`dllmain.cpp`中添加：
```cpp
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        OutputDebugStringA("ClipSHOOK injected successfully!");
        // ... 原有代码
        break;
    }
    return TRUE;
}
```

### 方法2：Process Explorer
1. 启动Process Explorer
2. 找到CSP进程
3. 查看加载的模块列表
4. 确认ClipSHOOK.dll已加载

### 方法3：创建文件标记
```cpp
case DLL_PROCESS_ATTACH:
    CreateFileA("C:\\temp\\clipshook_loaded.txt", GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, 0, NULL);
    break;
```

## 注意事项

1. **备份原始DLL** - 劫持前备份原始系统DLL
2. **测试兼容性** - 确保不影响CSP正常功能
3. **版本适配** - 不同CSP版本可能需要调整
4. **权限问题** - 可能需要管理员权限
5. **反病毒软件** - 可能被误报为恶意软件

## 故障排除

### DLL未加载
- 检查DLL是否在正确路径
- 验证导出函数是否正确
- 检查DLL依赖项

### CSP启动失败
- 恢复原始DLL
- 检查导出函数转发是否正确
- 验证DLL架构（x86/x64）匹配

### Hook功能不工作
- 确认DLL已成功注入
- 检查Hook目标地址是否正确
- 验证CSP版本兼容性
