#pragma once

//=============================================================================
// ClipSHOOK 项目配置文件
// 轻量级CSP Hook框架 - 只包含核心Hook功能所需的依赖
//=============================================================================

// 禁用CRT安全警告（用于兼容udis86库）
#define _CRT_SECURE_NO_WARNINGS

// 基础Windows API库
#pragma comment(lib, "User32.lib")    // 用户界面API
#pragma comment(lib, "Kernel32.lib")  // 核心系统API

// Hook功能所需的库
// udis86反汇编库已直接包含在项目中，支持AutoHook功能

//=============================================================================
// 项目信息
//=============================================================================
#define CLIPSHOOK_VERSION_MAJOR 1
#define CLIPSHOOK_VERSION_MINOR 0
#define CLIPSHOOK_VERSION_PATCH 0
#define CLIPSHOOK_VERSION_STRING "1.0.0"

// 项目名称
#define CLIPSHOOK_PROJECT_NAME L"ClipSHOOK"
#define CLIPSHOOK_DLL_NAME L"ClipSHOOK.dll"

// 调试配置
#ifdef _DEBUG
    #define CLIPSHOOK_DEBUG_MODE 1
#else
    #define CLIPSHOOK_DEBUG_MODE 0
#endif