# 手动查找checkData地址指南

## 🎯 目标

从您的日志可以看出，自动地址搜索失败了：
```
[ClipSHOOK] Simple Bypass - Address test at 0x143567B10: FAILED
[ClipSHOOK] Simple Bypass - Pattern search failed: FAILED
```

我们需要手动找到您的CSP版本中checkData函数的正确地址。

## 🔧 方法1：使用x64dbg查找

### 步骤1：准备工具
1. 下载并安装x64dbg
2. 确保CSP没有在运行

### 步骤2：附加到CSP进程
1. 启动CSP（不要关闭）
2. 启动x64dbg
3. 点击"File" -> "Attach"
4. 选择CLIPStudioPaint.exe进程

### 步骤3：搜索checkData函数

**方法A：搜索字符串**
1. 按Ctrl+F打开搜索
2. 选择"All Modules"
3. 搜索字符串"checkData"
4. 查看搜索结果，找到函数引用

**方法B：搜索错误消息**
1. 搜索保存相关的错误消息
2. 例如："trial"、"save"、"license"等
3. 从错误消息回溯到验证函数

**方法C：搜索特征码**
1. 搜索十六进制模式
2. 尝试以下特征码：
   - `48 89 5C 24 08 57 48 83 EC 20`
   - `40 53 48 83 EC 20 48 8B D9`
   - `48 89 5C 24 10 48 89 74 24 18`

### 步骤4：验证函数地址
1. 找到可能的地址后，在该地址设置断点
2. 在CSP中尝试保存文件
3. 如果断点被触发，说明找到了正确的函数

## 🔧 方法2：使用API Monitor

### 步骤1：安装API Monitor
1. 下载API Monitor
2. 以管理员权限运行

### 步骤2：监控CSP
1. 启动API Monitor
2. 选择CLIPStudioPaint.exe进程
3. 监控文件操作API（CreateFile、WriteFile等）

### 步骤3：分析调用堆栈
1. 在CSP中尝试保存文件
2. 查看API调用的堆栈
3. 找到验证相关的函数调用

## 🔧 方法3：使用Process Monitor

### 步骤1：启动Process Monitor
1. 下载并运行Process Monitor
2. 设置过滤器只显示CSP进程

### 步骤2：监控保存操作
1. 在CSP中尝试保存文件
2. 观察文件访问模式
3. 查找验证相关的注册表或文件访问

## 📋 配置找到的地址

找到checkData函数地址后，修改SimpleBypass.h：

```cpp
namespace Config {
    // 启用手动地址
    constexpr bool USE_MANUAL_ADDRESS = true;
    constexpr uintptr_t MANUAL_CHECKDATA_ADDRESS = 0x您找到的地址;
}
```

例如，如果找到地址是0x140123456：
```cpp
constexpr uintptr_t MANUAL_CHECKDATA_ADDRESS = 0x140123456;
```

## 🎯 常见的checkData函数特征

### 函数开头模式
```asm
push    rbx                 ; 53
sub     rsp, 20h           ; 48 83 EC 20
mov     rbx, rcx           ; 48 8B D9
```

### 或者
```asm
mov     [rsp+8], rbx       ; 48 89 5C 24 08
push    rdi                ; 57
sub     rsp, 20h           ; 48 83 EC 20
```

### 函数内容特征
- 通常包含条件判断
- 可能调用其他验证函数
- 返回bool值（true/false）

## 🔍 验证地址正确性

### 方法1：设置断点
1. 在x64dbg中在找到的地址设置断点
2. 在CSP中执行保存操作
3. 如果断点触发，说明地址正确

### 方法2：查看函数内容
1. 反汇编找到的地址
2. 检查是否包含验证逻辑
3. 查看函数的参数和返回值

### 方法3：测试Hook
1. 配置手动地址
2. 重新编译ClipSHOOK
3. 查看DebugView日志确认Hook成功

## 📊 预期的成功日志

配置正确地址后，应该看到：
```
[ClipSHOOK] Setting up simple bypass (NEW VERSION)...
[ClipSHOOK] Test hook installed successfully - Hook system working!
[ClipSHOOK] Simple Bypass - Using manual address at 0x您的地址: SUCCESS
[ClipSHOOK] Simple Bypass - checkData Hook installed at 0x您的地址: SUCCESS
[ClipSHOOK] Simple bypass setup completed successfully!

// 保存时应该看到
[ClipSHOOK] checkData called - returning TRUE (simple bypass)
```

## 🚨 如果仍然失败

### 可能的原因
1. **地址错误** - 找到的不是checkData函数
2. **函数签名不匹配** - 参数或调用约定不对
3. **多重验证** - CSP使用多个验证函数
4. **保护机制** - CSP有反Hook保护

### 替代方案
1. **Hook多个可能的函数**
2. **使用内存补丁而不是Hook**
3. **分析CSP的完整验证流程**
4. **寻找其他绕过点**

## 📞 需要帮助时提供的信息

如果需要进一步帮助，请提供：
1. **CSP版本号**
2. **找到的可能地址列表**
3. **x64dbg的搜索结果截图**
4. **函数反汇编代码**
5. **完整的DebugView日志**

## 🎯 快速测试方法

为了验证我们的Hook系统是否工作，新版本包含了一个测试Hook：
```
[ClipSHOOK] Test hook installed successfully - Hook system working!
[ClipSHOOK] TEST HOOK: GetCurrentProcessId called - Hook system working!
```

如果看到这个日志，说明Hook系统本身是正常的，问题只是地址不正确。

现在请使用x64dbg查找checkData函数的正确地址！
